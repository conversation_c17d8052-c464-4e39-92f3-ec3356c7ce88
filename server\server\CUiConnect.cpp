#include "CUiConnect.h"
#include "../../common/PacketStructure/UnifiedProtocol.h"
#include "../../common/PacketProcessing/UnifiedPacketHelper.h"
#include <iostream>
#include <PublicConfig.h>
#include "CAgentManager.h"


CUiConnect::CUiConnect() : m_ptrTcpServer(this)
{
    /* ITcpPullServer属性设置 */
    m_ptrTcpServer->SetMaxConnectionCount(1);
    m_ptrTcpServer->SetSendPolicy(SP_DIRECT);
    m_ptrTcpServer->SetNoDelay(TRUE);                           // 禁用 Nagle 算法，避免数据合包延迟，实时发包。
    m_ptrTcpServer->SetOnSendSyncPolicy(OSSP_RECEIVE);
    m_isControl = false;
}


CUiConnect& CUiConnect::Instance()
{
    static CUiConnect instance;
    return instance;
}

CONNID CUiConnect::GetUiConnID() const
{
    return m_uiConnID;
}

bool CUiConnect::SendToUI(const std::vector<uint8_t>& data)
{
    std::lock_guard<std::mutex> guard(m_lock);

    std::wcout << L"[CUiConnect::SendToUI] 状态检查: m_isControl=" << (m_isControl ? L"true" : L"false")
               << L", m_uiConnID=" << m_uiConnID
               << L", dataSize=" << data.size() << std::endl;

    if (m_isControl && m_uiConnID != 0) {
        bool result = m_ptrTcpServer->Send(m_uiConnID, data.data(), (int)data.size());
        std::wcout << L"[CUiConnect::SendToUI] 发送结果: " << (result ? L"成功" : L"失败") << std::endl;
        return result;
    } else {
        std::wcout << L"[CUiConnect::SendToUI] 发送失败: Control未连接或连接ID无效" << std::endl;
        return false;
    }
}

bool CUiConnect::Start(int port)
{
    return m_ptrTcpServer->Start(L"127.0.0.1", port);
}


EnHandleResult CUiConnect::OnClose(ITcpServer* pSender, CONNID dwConnID, EnSocketOperation enOperation, int iErrorCode)
{
    std::lock_guard<std::mutex> guard(m_lock);

    // 只有当断开的是当前Control连接时，才重置状态
    if (dwConnID == m_uiConnID) {
        m_uiConnID = 0;
        m_isControl = false;  // ✅ 修复：只有当前Control连接断开时才设置为false
        std::wcout << L"[CUiConnect] Control连接断开: connId=" << dwConnID << std::endl;
    } else {
        std::wcout << L"[CUiConnect] 非Control连接断开: connId=" << dwConnID << std::endl;
    }

    return HR_OK;
}

EnHandleResult CUiConnect::OnPrepareListen(ITcpServer* pSender, SOCKET soListen)
{
    //* 设置socket读写缓冲区
    SYS_SSO_SendBuffSize(soListen, MAX_SEND_BUFFER);
    SYS_SSO_RecvBuffSize(soListen, MAX_RECV_BUFFER);

    return HR_OK;
}

EnHandleResult CUiConnect::OnAccept(ITcpServer* pSender, CONNID dwConnID, UINT_PTR soClient)
{
    std::lock_guard<std::mutex> guard(m_lock);

    if (m_uiConnID != 0) {
        // 已存在连接，拒绝新连接
        std::wcout << L"[CUiConnect] 拒绝新连接，已存在Control连接: existingConnId=" << m_uiConnID
                   << L", newConnId=" << dwConnID << std::endl;
        pSender->Disconnect(dwConnID);
        return HR_IGNORE;
    }

    m_isControl = true;
    m_uiConnID = dwConnID;

    std::wcout << L"[CUiConnect] Control连接建立成功: connId=" << dwConnID
               << L", m_isControl=" << (m_isControl ? L"true" : L"false") << std::endl;

    return HR_OK;
}

EnHandleResult CUiConnect::OnReceive(ITcpServer* pSender, CONNID dwConnID, int iLength)
{
    /* 来自UI的统一协议数据包 */

    // 空数据直接忽略
    if (iLength <= 0) { return HR_ERROR; }

    // 如果不足以接收 UnifiedPacket 头部，则返回继续等待
    if (iLength < sizeof(UnifiedPacket)) {
        return HR_OK; // 不足一个包头，等下一次 OnReceive
    }

    /* 使用统一协议工具解析数据包 */

    // 接收完整数据包（头部 + 载荷）
    std::vector<BYTE> firstRecvBuffer;
    firstRecvBuffer.resize(sizeof(UnifiedPacket));
    if (EnFetchResult::FR_OK != m_ptrTcpServer->Fetch(dwConnID, firstRecvBuffer.data(), sizeof(UnifiedPacket))) {
        return HR_OK;
    }

    // 先解析数据包头以获取载荷长度
    UnifiedPacket tempHeader;
    if (!UnifiedPacketHelper::ParsePacketHeader(firstRecvBuffer.data(), firstRecvBuffer.size(), tempHeader)) {
        std::wcerr << L"[CUiConnect] 数据包头解析失败" << std::endl;
        return HR_ERROR;
    }

    // 校验载荷长度合法性
    const uint32_t MAX_PAYLOAD_SIZE = 10 * 1024 * 1024; // 10MB
    if (tempHeader.payloadLength == 0 || tempHeader.payloadLength > MAX_PAYLOAD_SIZE || tempHeader.payloadLength < sizeof(UnifiedHeader)) {
        std::wcerr << L"[CUiConnect] 载荷长度异常: " << tempHeader.payloadLength << std::endl;
        return HR_OK;
    }

    // 接收载荷数据
    std::vector<BYTE> payloadBuffer(tempHeader.payloadLength);
    if (EnFetchResult::FR_OK != m_ptrTcpServer->Fetch(dwConnID, payloadBuffer.data(), tempHeader.payloadLength)) {
        std::wcerr << L"[CUiConnect] 载荷数据获取失败" << std::endl;
        return HR_OK;
    }

    // ===== 传输层：解析传输层包头，解密载荷 =====

    // 传输层解析：只解析UnifiedPacket包头
    UnifiedPacket packet;
    if (!UnifiedPacketHelper::ParsePacketHeader(firstRecvBuffer.data(), firstRecvBuffer.size(), packet)) {
        std::wcerr << L"[CUiConnect] 传输层包头解析失败" << std::endl;
        return HR_ERROR;
    }

    // 传输层解密：解密载荷数据
    std::vector<BYTE> decryptedPayload = payloadBuffer;
    UnifiedPacketHelper::XorDecrypt(decryptedPayload, packet.xorKey);

    // 传输层校验：验证载荷完整性
    uint32_t calculatedChecksum = UnifiedPacketHelper::CalculateChecksum(decryptedPayload.data(), decryptedPayload.size());
    if (calculatedChecksum != packet.checksum) {
        std::wcerr << L"[CUiConnect] 传输层校验和验证失败" << std::endl;
        return HR_ERROR;
    }

    // ===== 路由层：解析业务头进行消息路由（特殊职责） =====

    // 路由层需要解析业务头来决定转发目标
    if (decryptedPayload.size() < sizeof(UnifiedHeader)) {
        std::wcerr << L"[CUiConnect] 载荷太小，无法包含业务头" << std::endl;
        return HR_ERROR;
    }

    UnifiedHeader header;
    std::memcpy(&header, decryptedPayload.data(), sizeof(UnifiedHeader));

    // 验证业务头
    if (!UnifiedProtocolUtils::IsValidHeader(header)) {
        std::wcerr << L"[CUiConnect] 业务头验证失败" << std::endl;
        return HR_OK;
    }

    std::wcout << L"[CUiConnect] 收到UI请求: fromClientId=" << packet.fromClientId
               << L", targetClientId=" << header.targetClientId
               << L", moduleType=" << (int)header.moduleType
               << L", subCommand=" << (int)header.subCommand << std::endl;

    // ===== 消息路由：根据目标类型转发完整业务载荷 =====
    if (header.targetType == static_cast<uint8_t>(TargetType::TARGET_AGENT)) {
        // 转发给指定的Agent客户端
        if (header.targetClientId != 0) {
            auto agentClient = CAgentManager::Instance().GetAgentClient(header.targetClientId);
            if (agentClient) {
                // 重要：传递完整的业务载荷（UnifiedHeader + 业务数据）
                // 让Agent的业务层自行解析
                agentClient->OnReceiveFromUI(decryptedPayload);
            } else {
                std::wcerr << L"[CUiConnect] 未找到目标Agent客户端: " << header.targetClientId << std::endl;
            }
        }
    } else if (header.targetType == static_cast<uint8_t>(TargetType::TARGET_SERVER)) {
        // 服务器本地处理
        std::wcout << L"[CUiConnect] 处理服务器本地请求" << std::endl;
        // TODO: 实现服务器本地请求处理，传递完整业务载荷
    }

    return HR_OK;
}

void CUiConnect::PrintStatus() const
{
    std::lock_guard<std::mutex> guard(m_lock);
    std::wcout << L"[CUiConnect::PrintStatus] m_isControl=" << (m_isControl ? L"true" : L"false")
               << L", m_uiConnID=" << m_uiConnID << std::endl;
}

// DispatchTask函数已删除，统一协议在OnReceive中直接处理路由


