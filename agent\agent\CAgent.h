#pragma once

#include "Header.h"
#include "Marco.h"
#include "ConfigTypes.h"
#include "UnifiedProtocol.h"
#include "ClientInfo.h"
#include "CInfoHelper.h"
#include "IProtocolAdapter.h"

// 新的模块管理相关头文件
#include "ModuleTypes.h"
#include "ModuleLifecycleManager.h"
#include "NetworkBindingManager.h"
#include "CallbackManager.h"
#include "MemoryModuleStorage.h"

// ===== 删除了转移功能相关头文件（彻底清理） =====

#include <memory>
#include <atomic>
#include <thread>
#include <mutex>
#include <chrono>
#include <condition_variable>


class CAgent
{
public:
    CAgent(const StartupConfig& startupConfig);
    ~CAgent();

    // 生命周期管理
    bool Start();
    void Stop();

    // 主连接命令处理（由CallbackManager调用）
    void HandleOnlineCommand(const UnifiedHeader& header, const std::vector<uint8_t>& data);
    void HandleHeartbeatCommand(const UnifiedHeader& header, const std::vector<uint8_t>& data);
    void HandleAgentManagementCommand(const UnifiedHeader& header, const std::vector<uint8_t>& data);
    void HandleMemoryModuleData(const UnifiedHeader& header, const std::vector<uint8_t>& data);

    // 删除了外部网络绑定请求处理函数（根据原始设计理念）

    // ===== 删除了转移功能处理函数（彻底清理） =====

    // 连接异常处理
    void HandlePrimaryConnectionLost();
    void HandleModuleConnectionLost(ModuleType moduleType);

    // 上线线程
    void OnlineLoopThread();

    // ===== 删除了转移模式控制函数（彻底清理） =====

    // 优雅退出机制
    void RequestShutdown();
    bool IsShuttingDown() const { return _shouldStop.load(); }

    // 上线状态查询
    enum class OnlineStatus {
        NOT_STARTED,    // 未开始
        IN_PROGRESS,    // 上线中
        SUCCESS,        // 上线成功
        TIMEOUT,        // 上线超时
        FAILED          // 上线失败
    };
    OnlineStatus GetOnlineStatus() const { return m_onlineStatus.load(); }

private:
    // 初始化
    bool InitializeManagers();
    bool SetupPrimaryConnection();

    // ===== 删除了手动网络绑定函数（改为完全自动化） =====

    // 删除了网络绑定响应发送函数（根据原始设计理念）
    // 删除了连接用途通知函数（根据原始设计理念）
    void SendOnlineRequest();
    void SendHeartbeatResponse();
    void SendUnsupportedResponse(const UnifiedHeader& originalHeader);

    // ===== 删除了转移功能辅助方法（彻底清理） =====

    // 信息收集
    bool CollectingInformation(std::string& collecInfo);

private:
    // 管理器
    std::unique_ptr<ModuleLifecycleManager> m_moduleManager;
    std::unique_ptr<NetworkBindingManager> m_networkManager;
    std::unique_ptr<CallbackManager> m_callbackManager;

    // 基础信息
    ClientInfo          _clientInfo;                // agent被控端信息: 基础与动态
    StartupConfig       _startupConfig;             // 启动配置（包含连接信息和权限）
    std::string         _name;                      // 主模块名称

    /* 上线逻辑 */
    std::atomic<bool> m_receivedOnlineAck{false};   // 请求上线包-应答后标识
    std::thread       m_onlineLoopThread;           // 上线线程
    std::atomic<OnlineStatus> m_onlineStatus{OnlineStatus::NOT_STARTED};  // 上线状态
    std::chrono::steady_clock::time_point m_onlineStartTime;  // 上线开始时间
    std::chrono::seconds m_onlineTimeout{300};       // 上线超时时间
    std::chrono::seconds m_retryInterval{30};        // 重试间隔
    std::condition_variable m_onlineCondition;      // 上线状态通知
    std::mutex        m_onlineStatusMutex;          // 上线状态锁

    /* 优雅退出机制 */
    std::atomic<bool> _shouldStop{false};           // 停止标志
    std::mutex        _shutdownMutex;               // 退出同步锁

    // ===== 删除了转移功能相关成员变量（彻底清理） =====
};

