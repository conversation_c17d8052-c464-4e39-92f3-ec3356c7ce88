#include "CAgentClient.h"
#include "../../common/PacketStructure/UnifiedProtocol.h"
#include "../../common/PacketProcessing/UnifiedPacketHelper.h"
#include "../../common/Config/DebugConfig.h"
#include <iostream>
#include <ChecksumUtils.h>
#include "CUiConnect.h"
#include "json.hpp"



CAgentClient::CAgentClient() {
    // 初始化各功能模块
    m_fileManager = std::make_unique<CFileManager>();
    m_shellManager = std::make_unique<CShellManager>();

    // ⭐ 设置模块的发送回调
    m_fileManager->SetSendCallback([this](const std::vector<BYTE>& data) {
        return this->SendToModule(m_fileManager->GetModuleType(), data);
    });

    m_shellManager->SetSendCallback([this](const std::vector<BYTE>& data) {
        return this->SendToModule(m_shellManager->GetModuleType(), data);
    });

    // 初始化连接状态跟踪
    _lastConnectionTime = 0;

    // 注册统一协议命令处理器
    m_dispatcher.Register(static_cast<UINT>(ModuleType::MODULE_ONLINE), [this](std::shared_ptr<SocketSession> pSession, std::vector<BYTE>& payload) {
        HandleOnlineCommand(pSession, payload);
        });
    m_dispatcher.Register(static_cast<UINT>(ModuleType::MODULE_HEARTBEAT), [this](std::shared_ptr<SocketSession> pSession, std::vector<BYTE>& payload) {
        HandleHeartbeat(pSession, payload);
        });
    m_dispatcher.Register(static_cast<UINT>(ModuleType::MODULE_FILE), [this](std::shared_ptr<SocketSession> pSession, std::vector<BYTE>& payload) {
        UpdateModuleConnection(ModuleType::MODULE_FILE, pSession);
        m_fileManager->OnReceive(payload);
        });
    m_dispatcher.Register(static_cast<UINT>(ModuleType::MODULE_SHELL), [this](std::shared_ptr<SocketSession> pSession, std::vector<BYTE>& payload) {
        UpdateModuleConnection(ModuleType::MODULE_SHELL, pSession);
        m_shellManager->OnReceive(payload);
        });

}

CAgentClient::~CAgentClient() {
    std::wcout << L"[~CAgentClient] 析构Agent客户端: clientId=" << _clientInfo.base.clientId << std::endl;

    // 清理所有连接
    CleanupAllConnections();

    // 自动析构 m_fileManager 与 m_shellManager
}

void CAgentClient::OnReceive(std::shared_ptr<SocketSession> pSession, std::vector<BYTE>& payload){
    if (payload.empty()) {
        std::wcerr << L"CAgentClient::OnReceive: 接收到空数据" << std::endl;
        return;
    }

    // 基础验证载荷格式
    if (payload.size() < sizeof(UnifiedHeader)) {
        std::wcerr << L"CAgentClient::OnReceive: 数据包太小，无法包含业务头" << std::endl;
        return;
    }

    // 解析业务头以获取模块类型（用于分发）
    UnifiedHeader header;
    std::memcpy(&header, payload.data(), sizeof(UnifiedHeader));

    // 验证业务头
    if (!UnifiedProtocolUtils::IsValidHeader(header)) {
        std::wcerr << L"CAgentClient::OnReceive: 业务头验证失败" << std::endl;
        return;
    }

    // 记录接收信息
    std::wcout << L"[CAgentClient] 接收到数据包: targetType=" << (int)header.targetType
               << L", targetClientId=" << header.targetClientId
               << L", moduleType=" << (int)header.moduleType
               << L", subCommand=" << (int)header.subCommand
               << L", statusCode=" << (int)header.statusCode << std::endl;

    // 根据模块类型分发命令，传递完整载荷（UnifiedHeader + BusinessData）
    UINT moduleId = static_cast<UINT>(header.moduleType);
    m_dispatcher.Dispatch(moduleId, pSession, payload);
}

void CAgentClient::OnReceiveFromUI(std::vector<BYTE>& payload)
{
    std::wcout << L"CAgentClient::OnReceiveFromUI: 处理来自Control的任务包" << std::endl;

    // 基础验证载荷格式
    if (payload.size() < sizeof(UnifiedHeader)) {
        std::wcerr << L"[OnReceiveFromUI] 数据包太小，无法包含业务头" << std::endl;
        return;
    }

    // 解析业务头以获取模块类型
    UnifiedHeader header;
    std::memcpy(&header, payload.data(), sizeof(UnifiedHeader));

    // 验证业务头
    if (!UnifiedProtocolUtils::IsValidHeader(header)) {
        std::wcerr << L"[OnReceiveFromUI] 业务头验证失败" << std::endl;
        return;
    }

    ModuleType moduleType = static_cast<ModuleType>(header.moduleType);
    std::wcout << L"[OnReceiveFromUI] Control任务包: moduleType=" << (int)moduleType
               << L", subCommand=" << (int)header.subCommand
               << L", targetClientId=" << header.targetClientId << std::endl;

    // ⭐ 关键：根据模块类型获取对应的专用连接
    auto pSession = GetModuleConnection(moduleType);
    if (!pSession || !pSession->isActive) {
        std::wcerr << L"[OnReceiveFromUI] 模块 " << (int)moduleType
                   << L" 没有可用连接，无法执行任务" << std::endl;

        // 发送错误响应给Control
        SendErrorResponseToControl(header, "Module connection not available");
        return;
    }

    std::wcout << L"[OnReceiveFromUI] 找到模块连接: moduleType=" << (int)moduleType
               << L", connId=" << pSession->connId << std::endl;

    // ⭐ 直接使用已获取的pSession发送（避免重复查找）
    auto server = pSession->m_ISCptr.lock();
    if (!server) {
        std::wcerr << L"[OnReceiveFromUI] ServerBase无效: moduleType=" << (int)moduleType << std::endl;
        SendErrorResponseToControl(header, "Server adapter not available");
        return;
    }

    // 直接发送原始payload到Agent模块
    server->Send(pSession, const_cast<LPBYTE>(payload.data()), (UINT)payload.size());

    std::wcout << L"[OnReceiveFromUI] 成功转发Control任务到模块: moduleType="
               << (int)moduleType << L", connId=" << pSession->connId
               << L", size=" << payload.size() << std::endl;
}


void CAgentClient::HandleHeartbeat(std::shared_ptr<SocketSession> pSession, std::vector<BYTE>& payload) {
    std::wcout << L"HandleHeartbeat: 处理心跳包，客户端ID: " << pSession->clientId << std::endl;

    // 解析统一协议业务头
    if (payload.size() < sizeof(UnifiedHeader)) {
        std::wcerr << L"HandleHeartbeat: 数据包太小，无法包含业务头" << std::endl;
        return;
    }

    UnifiedHeader header;
    std::memcpy(&header, payload.data(), sizeof(UnifiedHeader));

    // 提取业务数据（心跳包可能包含ClientStateInfo）
    std::vector<BYTE> businessData;
    if (payload.size() > sizeof(UnifiedHeader)) {
        businessData.assign(
            payload.begin() + sizeof(UnifiedHeader),
            payload.end()
        );
    }

    switch (header.subCommand) {
    case SubCommand::Heartbeat::HEARTBEAT_PONG: {
        std::wcout << L"[HandleHeartbeat] 收到心跳PONG响应" << std::endl;

        // 解析ClientStateInfo（如果有）
        if (!businessData.empty()) {
            try {
                std::string stateJson(reinterpret_cast<const char*>(businessData.data()), businessData.size());
                nlohmann::json j = nlohmann::json::parse(stateJson);
                from_json(j, _clientInfo.state);
                std::wcout << L"[HandleHeartbeat] 成功更新客户端状态信息" << std::endl;
            }
            catch (const std::exception& e) {
                std::wcerr << L"[HandleHeartbeat] 状态信息解析失败: " << e.what() << std::endl;
            }
        }

        // 更新心跳时间
        UpdateHeartbeat();
        break;
    }
    default:
        std::wcout << L"[HandleHeartbeat] 未知的心跳子命令: " << (int)header.subCommand << std::endl;
        break;
    }
}


void CAgentClient::HandleOnlineCommand(std::shared_ptr<SocketSession> pSession, std::vector<BYTE>& payload)
{
    std::wcout << L"HandleOnlineCommand: 处理上线命令，客户端ID: " << pSession->clientId << std::endl;

    // 解析统一协议业务头
    if (payload.size() < sizeof(UnifiedHeader)) {
        std::wcerr << L"HandleOnlineCommand: 数据包太小，无法包含业务头" << std::endl;
        return;
    }

    UnifiedHeader header;
    std::memcpy(&header, payload.data(), sizeof(UnifiedHeader));

    // 提取业务数据
    std::vector<BYTE> businessData;
    if (payload.size() > sizeof(UnifiedHeader)) {
        businessData.assign(
            payload.begin() + sizeof(UnifiedHeader),
            payload.end()
        );
    }

    std::wcout << L"[HandleOnlineCommand] 子命令: " << (int)header.subCommand << std::endl;

    switch (header.subCommand)
    {
    case SubCommand::Online::ONLINE_REQUEST: {

        std::wcout << L"[HandleOnlineCommand] 处理上线请求，客户端ID: " << pSession->clientId << std::endl;

        // 构建统一协议响应头
        UnifiedHeader responseHeader;
        responseHeader.targetType = static_cast<uint8_t>(TargetType::TARGET_AGENT);
        responseHeader.targetClientId = pSession->clientId;  // 目标是发送请求的Agent
        responseHeader.moduleType = static_cast<uint8_t>(ModuleType::MODULE_ONLINE);
        responseHeader.subCommand = SubCommand::Online::ONLINE_ACK;
        responseHeader.statusCode = static_cast<uint8_t>(StatusCode::_STATUS_SUCCESS);

        // 构建响应数据包
        std::vector<BYTE> responseData;
        responseData.insert(responseData.end(),
            reinterpret_cast<BYTE*>(&responseHeader),
            reinterpret_cast<BYTE*>(&responseHeader) + sizeof(UnifiedHeader));

        // 使用统一协议发送
        std::vector<BYTE> unifiedPacket;
        bool status = UnifiedPacketHelper::BuildUnifiedPacket(
            SERVER_CLIENT_ID,  // 服务器作为发送方
            responseHeader,
            std::vector<BYTE>(),  // 无额外载荷数据
            unifiedPacket
        );

        if (status) {
            // ⭐ 修复：使用pSession自己的适配器
            auto adapter = pSession->m_ISCptr.lock();
            if (adapter) {
                adapter->Send(pSession, unifiedPacket.data(), (UINT)unifiedPacket.size());
                std::wcout << L"[HandleOnlineCommand] 上线确认已发送: connId=" << pSession->connId << std::endl;
            } else {
                std::wcerr << L"[HandleOnlineCommand] pSession适配器无效，发送失败" << std::endl;
            }
        }

        break;
    }
    case SubCommand::Online::ONLINE_INFO_REPORT: {

        std::wcout << L"[HandleOnlineCommand] 处理信息上报，数据大小: " << businessData.size() << std::endl;

        // 解析Agent发送的静态信息（JSON格式）
        if (!businessData.empty()) {
            std::string body(reinterpret_cast<const char*>(businessData.data()), businessData.size());

            try {
                nlohmann::json j = nlohmann::json::parse(body);
                from_json(j, _clientInfo.base);
                std::wcout << L"[HandleOnlineCommand] 成功解析客户端信息，计算机名: "
                          << std::wstring(_clientInfo.base.computerName.begin(), _clientInfo.base.computerName.end()) << std::endl;
            }
            catch (const std::exception& e) {
                std::wcerr << L"[HandleOnlineCommand] JSON解析失败: " << e.what() << std::endl;
                return;
            }
        }


        // 回复信息确认包
        UnifiedHeader infoAckHeader;
        infoAckHeader.targetType = static_cast<uint8_t>(TargetType::TARGET_AGENT);
        infoAckHeader.targetClientId = pSession->clientId;
        infoAckHeader.moduleType = static_cast<uint8_t>(ModuleType::MODULE_ONLINE);
        infoAckHeader.subCommand = SubCommand::Online::ONLINE_INFO_ACK;
        infoAckHeader.statusCode = static_cast<uint8_t>(StatusCode::_STATUS_SUCCESS);

        // 发送信息确认
        std::vector<BYTE> infoAckData;
        infoAckData.insert(infoAckData.end(),
            reinterpret_cast<BYTE*>(&infoAckHeader),
            reinterpret_cast<BYTE*>(&infoAckHeader) + sizeof(UnifiedHeader));

        std::vector<BYTE> infoAckPacket;
        bool ackStatus = UnifiedPacketHelper::BuildUnifiedPacket(
            SERVER_CLIENT_ID,
            infoAckHeader,
            std::vector<BYTE>(),
            infoAckPacket
        );

        if (ackStatus) {
            // ⭐ 修复：使用pSession自己的适配器
            auto adapter = pSession->m_ISCptr.lock();
            if (adapter) {
                adapter->Send(pSession, infoAckPacket.data(), (UINT)infoAckPacket.size());
                std::wcout << L"[HandleOnlineCommand] 信息确认已发送: connId=" << pSession->connId << std::endl;
            } else {
                std::wcerr << L"[HandleOnlineCommand] pSession适配器无效，发送失败" << std::endl;
            }
        }


        // 数据库信息处理
        // TODO: 将客户端信息存储到数据库

        // 发送上线通知到UI（使用统一协议）
        UnifiedHeader uiNotifyHeader;
        uiNotifyHeader.targetType = static_cast<uint8_t>(TargetType::TARGET_CONTROL);
        uiNotifyHeader.targetClientId = CONTROL_CLIENT_ID;
        uiNotifyHeader.moduleType = static_cast<uint8_t>(ModuleType::MODULE_ONLINE);
        uiNotifyHeader.subCommand = SubCommand::Online::ONLINE_INFO_REPORT;
        uiNotifyHeader.statusCode = static_cast<uint8_t>(StatusCode::_STATUS_SUCCESS);

        // 发送到UI
        SendUIUnified(uiNotifyHeader, businessData);
        std::wcout << L"[HandleOnlineCommand] 上线通知已发送到UI" << std::endl;

        break;
    }

    default:
        std::wcerr << L"[HandleOnlineCommand] 未知的上线子命令: " << (int)header.subCommand << std::endl;
        break;
    }
}

void CAgentClient::UpdateHeartbeat()
{
    _clientInfo.state.lastHeartbeatTime = time(nullptr);
    _lastConnectionTime = time(nullptr);  // 同时更新连接时间

    std::wcout << L"[UpdateHeartbeat] 心跳更新: clientId=" << _clientInfo.base.clientId
               << L", time=" << _clientInfo.state.lastHeartbeatTime << std::endl;
}

void CAgentClient::SetClientId(uint32_t clientId)
{
    if (clientId == 0) {
        std::wcerr << L"[SetClientId] 警告：设置了无效的clientId=0" << std::endl;
    }

    std::lock_guard<std::mutex> lock(m_stateMutex);
    _clientInfo.base.clientId = clientId;
    std::wcout << L"[SetClientId] 设置客户端ID: " << clientId << std::endl;
}

time_t CAgentClient::GetLastHeartbeatTime() const
{
    std::lock_guard<std::mutex> lock(m_stateMutex);
    return _clientInfo.state.lastHeartbeatTime;
}

bool CAgentClient::IsOnline() const
{
    std::lock_guard<std::mutex> lock(m_stateMutex);
    time_t now = time(nullptr);
    time_t lastHeartbeat = _clientInfo.state.lastHeartbeatTime;

    // 使用统一配置：心跳超时检查
    const time_t HEARTBEAT_TIMEOUT = GET_HEARTBEAT_TIMEOUT();
    return (now - lastHeartbeat) <= HEARTBEAT_TIMEOUT;
}


// 统一协议SendUI函数
bool CAgentClient::SendUIUnified(const UnifiedHeader& header, const std::vector<BYTE>& payload)
{
    // 调试：检查CUiConnect状态
    std::wcout << L"[SendUIUnified] 开始发送，先检查CUiConnect状态:" << std::endl;
    CUiConnect::Instance().PrintStatus();

    std::vector<BYTE> unifiedPacket;
    bool status = UnifiedPacketHelper::BuildUnifiedPacket(
        SERVER_CLIENT_ID,  // 服务器作为发送方
        header,
        payload,
        unifiedPacket
    );

    if (status) {
        std::wcout << L"[SendUIUnified] 数据包构建成功，准备发送到UI，大小=" << unifiedPacket.size() << std::endl;
        bool sendResult = CUiConnect::Instance().SendToUI(unifiedPacket);
        std::wcout << L"[SendUIUnified] 发送结果: " << (sendResult ? L"成功" : L"失败") << std::endl;
        return sendResult;
    } else {
        std::wcout << L"[SendUIUnified] 数据包构建失败" << std::endl;
        return false;
    }
}

// 统一协议SendAgent函数
bool CAgentClient::SendAgent(std::shared_ptr<SocketSession> pSession, const UnifiedHeader& header, const std::vector<BYTE>& payload)
{
    std::vector<BYTE> unifiedPacket;
    bool status = UnifiedPacketHelper::BuildUnifiedPacket(
        SERVER_CLIENT_ID,  // 服务器作为发送方
        header,
        payload,
        unifiedPacket
    );

    if (status) {
        // ⭐ 修复：直接使用传入的pSession的适配器，而不是_netAdapter
        auto adapter = pSession->m_ISCptr.lock();
        if (adapter) {
            adapter->Send(pSession, unifiedPacket.data(), (UINT)unifiedPacket.size());
            std::wcout << L"[SendAgent] 统一协议数据包已发送: connId=" << pSession->connId
                       << L", targetClientId=" << header.targetClientId << std::endl;
        } else {
            std::wcerr << L"[SendAgent] pSession的ServerBase无效，发送失败" << std::endl;
            status = false;
        }
    }

    return status;
}

// 发送错误响应给Control
void CAgentClient::SendErrorResponseToControl(const UnifiedHeader& originalHeader, const std::string& errorMessage)
{
    UnifiedHeader errorHeader = originalHeader;
    errorHeader.targetType = static_cast<uint8_t>(TargetType::TARGET_CONTROL);
    errorHeader.targetClientId = 0;  // 发送给Control
    errorHeader.statusCode = static_cast<uint8_t>(StatusCode::_STATUS_FAILED);

    std::vector<BYTE> errorData(errorMessage.begin(), errorMessage.end());
    SendUIUnified(errorHeader, errorData);

    std::wcout << L"[SendErrorResponseToControl] 已发送错误响应给Control: "
               << errorMessage.c_str() << std::endl;
}


// 检查是否有指定模块的连接（使用新系统）
bool CAgentClient::HasModuleConnection(ModuleType moduleType)
{
    auto it = m_moduleConnections.find(moduleType);
    return (it != m_moduleConnections.end() && it->second && it->second->isActive);
}

// 根据连接ID移除连接（新系统）
void CAgentClient::RemoveConnectionByConnId(CONNID connId)
{
    for (auto it = m_moduleConnections.begin(); it != m_moduleConnections.end(); ++it) {
        if (it->second && it->second->connId == connId) {
            ModuleType moduleType = it->first;

            std::wcout << L"[RemoveConnectionByConnId] 移除模块连接: clientId=" << _clientInfo.base.clientId
                       << L", moduleType=" << (int)moduleType
                       << L", connId=" << connId << std::endl;

            // 移除连接
            m_moduleConnections.erase(it);

            // 处理连接丢失
            HandleConnectionLost(connId);

            std::wcout << L"[RemoveConnectionByConnId] 移除完成: clientId=" << _clientInfo.base.clientId << std::endl;
            break;
        }
    }
}

// ===== 网络异常处理方法 =====

// 处理连接丢失
void CAgentClient::HandleConnectionLost(CONNID connId)
{
    std::wcout << L"[HandleConnectionLost] 处理连接丢失: clientId=" << _clientInfo.base.clientId
               << L", connId=" << connId << std::endl;

    // 检查是否还有其他活跃连接（使用新系统）
    bool hasActiveConnection = false;
    for (const auto& pair : m_moduleConnections) {
        if (pair.second && pair.second->isActive) {
            hasActiveConnection = true;
            break;
        }
    }

    if (!hasActiveConnection) {
        std::wcout << L"[HandleConnectionLost] Agent完全断线: clientId=" << _clientInfo.base.clientId << std::endl;

        // 通知Control Agent离线
        UnifiedHeader offlineHeader;
        offlineHeader.targetType = static_cast<uint8_t>(TargetType::TARGET_CONTROL);
        offlineHeader.targetClientId = 0;  // 发送给Control
        offlineHeader.moduleType = static_cast<uint8_t>(ModuleType::MODULE_ONLINE);
        offlineHeader.subCommand = static_cast<uint8_t>(SubCommand::Online::ONLINE_INFO_REPORT);  // 使用现有的子命令
        offlineHeader.statusCode = static_cast<uint8_t>(StatusCode::_STATUS_FAILED);

        SendUIUnified(offlineHeader);
    } else {
        std::wcout << L"[HandleConnectionLost] Agent部分连接丢失，等待恢复" << std::endl;
    }
}


// 检查Agent是否完全连接（使用新系统）
bool CAgentClient::IsAgentFullyConnected()
{
    // 检查核心模块是否都有连接
    std::vector<ModuleType> requiredModules = {
        ModuleType::MODULE_HEARTBEAT,  // 心跳模块（主控制）
        ModuleType::MODULE_FILE,       // 文件模块
        ModuleType::MODULE_SHELL       // Shell模块
    };

    for (ModuleType moduleType : requiredModules) {
        auto it = m_moduleConnections.find(moduleType);
        if (it == m_moduleConnections.end() || !it->second || !it->second->isActive) {
            return false;
        }
    }
    return true;
}

// 获取缺失的模块连接类型
std::vector<ModuleType> CAgentClient::GetMissingModules()
{
    std::vector<ModuleType> missing;
    std::vector<ModuleType> requiredModules = {
        ModuleType::MODULE_HEARTBEAT,
        ModuleType::MODULE_FILE,
        ModuleType::MODULE_SHELL
    };

    for (ModuleType moduleType : requiredModules) {
        auto it = m_moduleConnections.find(moduleType);
        if (it == m_moduleConnections.end() || !it->second || !it->second->isActive) {
            missing.push_back(moduleType);
        }
    }

    return missing;
}



// ===== ⭐ 新增：模块连接管理方法 =====

// 更新模块连接（保持最新）
void CAgentClient::UpdateModuleConnection(ModuleType moduleType, std::shared_ptr<SocketSession> pSession) {
    if (!pSession) return;

    std::lock_guard<std::mutex> lock(m_connectionMutex);
    m_moduleConnections[moduleType] = pSession;
    std::wcout << L"[CAgentClient] 更新模块连接: clientId=" << _clientInfo.base.clientId
               << L", moduleType=" << (int)moduleType
               << L", connId=" << pSession->connId << std::endl;
}

// 发送数据到指定模块
bool CAgentClient::SendToModule(ModuleType moduleType, const std::vector<BYTE>& data) {
    std::shared_ptr<SocketSession> pSession;

    // 线程安全地获取连接
    {
        std::lock_guard<std::mutex> lock(m_connectionMutex);
        auto it = m_moduleConnections.find(moduleType);
        if (it == m_moduleConnections.end() || !it->second) {
            std::wcerr << L"[CAgentClient] 模块连接不存在: clientId=" << _clientInfo.base.clientId
                       << L", moduleType=" << (int)moduleType << std::endl;
            return false;
        }
        pSession = it->second;  // 复制shared_ptr
    }

    // 在锁外进行网络操作
    if (!pSession->isActive) {
        std::wcerr << L"[CAgentClient] 模块连接已断开: clientId=" << _clientInfo.base.clientId
                   << L", moduleType=" << (int)moduleType
                   << L", connId=" << pSession->connId << std::endl;
        return false;
    }

    // 发送数据
    auto server = pSession->m_ISCptr.lock();
    if (server) {
        server->Send(pSession, const_cast<LPBYTE>(data.data()), (UINT)data.size());
        std::wcout << L"[CAgentClient] 发送成功: clientId=" << _clientInfo.base.clientId
                   << L", moduleType=" << (int)moduleType
                   << L", size=" << data.size() << std::endl;
        return true;
    }

    std::wcerr << L"[CAgentClient] ServerBase无效，发送失败: clientId=" << _clientInfo.base.clientId
               << L", moduleType=" << (int)moduleType << std::endl;
    return false;
}

// 获取模块连接
std::shared_ptr<SocketSession> CAgentClient::GetModuleConnection(ModuleType moduleType) {
    auto it = m_moduleConnections.find(moduleType);
    return (it != m_moduleConnections.end()) ? it->second : nullptr;
}

// 移除模块连接
void CAgentClient::RemoveModuleConnection(ModuleType moduleType) {
    auto it = m_moduleConnections.find(moduleType);
    if (it != m_moduleConnections.end()) {
        std::wcout << L"[CAgentClient] 移除模块连接: clientId=" << _clientInfo.base.clientId
                   << L", moduleType=" << (int)moduleType << std::endl;
        m_moduleConnections.erase(it);
    }
}

// ⭐ 已删除：重复的RemoveConnectionByConnId实现（保留第427行的版本）

// 获取活跃的模块列表
std::vector<ModuleType> CAgentClient::GetActiveModules() const {
    std::vector<ModuleType> result;
    for (const auto& pair : m_moduleConnections) {
        if (pair.second && pair.second->isActive) {
            result.push_back(pair.first);
        }
    }
    return result;
}

// 获取活跃连接数量
size_t CAgentClient::GetActiveConnectionCount() const
{
    std::lock_guard<std::mutex> lock(m_connectionMutex);
    size_t count = 0;
    for (const auto& pair : m_moduleConnections) {
        if (pair.second && pair.second->isActive) {
            count++;
        }
    }
    return count;
}

// 清理所有连接
void CAgentClient::CleanupAllConnections()
{
    std::lock_guard<std::mutex> lock(m_connectionMutex);

    std::wcout << L"[CleanupAllConnections] 清理所有连接: clientId=" << _clientInfo.base.clientId
               << L", 连接数=" << m_moduleConnections.size() << std::endl;

    m_moduleConnections.clear();

    std::lock_guard<std::mutex> stateLock(m_stateMutex);
    _lastConnectionTime = 0;
}

// ===== ⭐ 新增：类型转换工具 =====

ModuleType CAgentClient::ConvertPurposeToModuleType(ConnectionPurpose purpose) {
    switch (purpose) {
        case PURPOSE_FILE_TRANSFER:
            return ModuleType::MODULE_FILE;
        case PURPOSE_SHELL_CONTROL:
            return ModuleType::MODULE_SHELL;
        case PURPOSE_SCREEN_SHARE:
            return ModuleType::MODULE_SCREENSHOT;
        case PURPOSE_PRIMARY_CONTROL:
            return ModuleType::MODULE_ONLINE;
        case PURPOSE_HEARTBEAT_ONLY:
            return ModuleType::MODULE_HEARTBEAT;
        case PURPOSE_BULK_DATA:
        case PURPOSE_UNKNOWN:
        default:
            // 对于未知或不支持的用途，返回心跳模块作为默认值
            return ModuleType::MODULE_HEARTBEAT;
    }
}
