#pragma once

#include <HPSocket.h>
#include <SocketInterface.h>
#include <memory>   // 用于 std::shared_ptr
#include <ctime>    // 用于 time_t
#include <string>   // 用于 std::wstring
#include <vector>
#include <mutex>



// 回调函数
typedef void(__stdcall* UINOTIFYPROC)(std::vector<uint8_t> data);



class CUiConnect : public CTcpPullServerListener
{
    enum {
        MAX_SEND_BUFFER = 65535,	// 最大发送数据长度 1024*64
        MAX_RECV_BUFFER = 65535,    // 最大接收数据长度
    };

public:
    static CUiConnect& Instance();
    CONNID GetUiConnID() const;
    bool SendToUI(const std::vector<uint8_t>& data);
    bool Start(int port);

    // 调试函数
    bool IsControlConnected() const { return m_isControl; }
    void PrintStatus() const;

private:
    CUiConnect(); // 私有构造
    CUiConnect(const CUiConnect&) = delete;
    CUiConnect& operator=(const CUiConnect&) = delete;

private:
    virtual EnHandleResult OnClose(ITcpServer* pSender, CONNID dwConnID, EnSocketOperation enOperation, int iErrorCode) override;
    virtual EnHandleResult OnPrepareListen(ITcpServer* pSender, SOCKET soListen) override;
    virtual EnHandleResult OnAccept(ITcpServer* pSender, CONNID dwConnID, UINT_PTR soClient) override;
    virtual EnHandleResult OnReceive(ITcpServer* pSender, CONNID dwConnID, int iLength) override;

private:
    UINOTIFYPROC	            m_pUiNotifyProc;        // 回调函数指针
    CTcpPullServerPtr		    m_ptrTcpServer;		    // hp-socket handle (ITcpPullServer 对象智能指针)
    mutable std::mutex          m_lock;                 // 锁
    CONNID                      m_uiConnID;             // ui的会话
    bool                        m_isControl;            // 是否有control连接

private:



};


